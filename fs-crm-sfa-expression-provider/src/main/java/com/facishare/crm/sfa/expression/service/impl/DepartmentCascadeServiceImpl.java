package com.facishare.crm.sfa.expression.service.impl;

import com.facishare.crm.sfa.expression.service.DepartmentCascadeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 部门级联查询服务实现类
 * 
 * <AUTHOR>
 * @date 2025/01/16
 */
@Service
@Slf4j
public class DepartmentCascadeServiceImpl implements DepartmentCascadeService {
    
    // TODO: 注入实际的部门服务，这里需要根据项目实际情况调整
    // @Autowired
    // private DepartmentService departmentService;
    
    @Override
    public List<String> getAllSubDepartmentIds(String tenantId, List<String> departmentIds) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        
        Set<String> allDeptIds = Sets.newHashSet();
        
        for (String deptId : departmentIds) {
            if (StringUtils.isNotBlank(deptId)) {
                // 添加当前部门ID
                allDeptIds.add(deptId);
                
                // 获取所有子部门ID
                List<String> subDeptIds = getSubDepartmentIds(tenantId, deptId);
                allDeptIds.addAll(subDeptIds);
            }
        }
        
        return Lists.newArrayList(allDeptIds);
    }
    
    @Override
    public boolean isEmployeeInDepartment(String tenantId, String employeeId, List<String> departmentIds) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId) || CollectionUtils.isEmpty(departmentIds)) {
            return false;
        }
        
        try {
            // 获取员工所属的部门ID
            String employeeDeptId = getEmployeeDepartmentId(tenantId, employeeId);
            if (StringUtils.isBlank(employeeDeptId)) {
                return false;
            }
            
            // 获取所有目标部门及其子部门ID
            List<String> allTargetDeptIds = getAllSubDepartmentIds(tenantId, departmentIds);
            
            // 判断员工部门是否在目标部门列表中
            return allTargetDeptIds.contains(employeeDeptId);
            
        } catch (Exception e) {
            log.error("Failed to check if employee {} is in departments {}, tenantId: {}", 
                    employeeId, departmentIds, tenantId, e);
            return false;
        }
    }
    
    @Override
    public boolean isDepartmentInParent(String tenantId, String targetDepartmentId, List<String> parentDepartmentIds) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(targetDepartmentId) || CollectionUtils.isEmpty(parentDepartmentIds)) {
            return false;
        }
        
        try {
            // 获取所有父部门及其子部门ID
            List<String> allParentDeptIds = getAllSubDepartmentIds(tenantId, parentDepartmentIds);
            
            // 判断目标部门是否在父部门列表中
            return allParentDeptIds.contains(targetDepartmentId);
            
        } catch (Exception e) {
            log.error("Failed to check if department {} is in parent departments {}, tenantId: {}", 
                    targetDepartmentId, parentDepartmentIds, tenantId, e);
            return false;
        }
    }
    
    /**
     * 获取指定部门的所有子部门ID（递归）
     * 
     * @param tenantId 企业ID
     * @param departmentId 部门ID
     * @return 子部门ID列表
     */
    private List<String> getSubDepartmentIds(String tenantId, String departmentId) {
        // TODO: 这里需要调用实际的部门服务API来获取子部门
        // 示例实现，需要根据实际项目调整
        
        try {
            // 方案1：调用部门服务API
            // return departmentService.getSubDepartmentIds(tenantId, departmentId);
            
            // 方案2：临时实现，返回空列表（需要替换为实际实现）
            log.warn("getSubDepartmentIds not implemented, returning empty list for deptId: {}", departmentId);
            return Lists.newArrayList();
            
        } catch (Exception e) {
            log.error("Failed to get sub department ids for dept: {}, tenantId: {}", departmentId, tenantId, e);
            return Lists.newArrayList();
        }
    }
    
    /**
     * 获取员工所属的部门ID
     * 
     * @param tenantId 企业ID
     * @param employeeId 员工ID
     * @return 部门ID
     */
    private String getEmployeeDepartmentId(String tenantId, String employeeId) {
        // TODO: 这里需要调用实际的员工服务API来获取员工部门
        // 示例实现，需要根据实际项目调整
        
        try {
            // 方案1：调用员工服务API
            // return employeeService.getEmployeeDepartmentId(tenantId, employeeId);
            
            // 方案2：临时实现，返回null（需要替换为实际实现）
            log.warn("getEmployeeDepartmentId not implemented, returning null for employeeId: {}", employeeId);
            return null;
            
        } catch (Exception e) {
            log.error("Failed to get employee department id for employee: {}, tenantId: {}", employeeId, tenantId, e);
            return null;
        }
    }
}
