package com.facishare.crm.sfa.expression.function;

import com.facishare.crm.sfa.expression.service.DepartmentCascadeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 部门相关的表达式函数扩展
 * 
 * <AUTHOR>
 * @date 2025/01/16
 */
@Component
@Slf4j
public class DepartmentExpressionFunction {
    
    // TODO: 取消注释并注入实际的部门级联服务
    // @Autowired
    // private DepartmentCascadeService departmentCascadeService;
    
    /**
     * 判断指定值是否属于部门（包含子部门）
     * 用于表达式引擎中的 INDEPARTMENT 函数
     * 
     * @param fieldValue 字段值（员工ID或部门ID）
     * @param departmentIds 部门ID列表，用逗号分隔
     * @return 如果属于指定部门或其子部门，返回true
     */
    public boolean inDepartment(String fieldValue, String departmentIds) {
        return inDepartment(fieldValue, departmentIds, null);
    }
    
    /**
     * 判断指定值是否属于部门（包含子部门）
     * 
     * @param fieldValue 字段值（员工ID或部门ID）
     * @param departmentIds 部门ID列表，用逗号分隔
     * @param tenantId 企业ID（从上下文获取）
     * @return 如果属于指定部门或其子部门，返回true
     */
    public boolean inDepartment(String fieldValue, String departmentIds, String tenantId) {
        if (StringUtils.isBlank(fieldValue) || StringUtils.isBlank(departmentIds)) {
            return false;
        }
        
        try {
            // 解析部门ID列表
            List<String> deptIdList = parseDepartmentIds(departmentIds);
            if (deptIdList.isEmpty()) {
                return false;
            }
            
            // 获取企业ID（这里需要根据实际情况获取）
            String actualTenantId = getTenantIdFromContext(tenantId);
            if (StringUtils.isBlank(actualTenantId)) {
                log.warn("Cannot get tenantId for inDepartment function");
                return false;
            }
            
            // TODO: 取消注释并使用实际的部门级联服务
            // 方案1：如果fieldValue是员工ID
            // return departmentCascadeService.isEmployeeInDepartment(actualTenantId, fieldValue, deptIdList);
            
            // 方案2：如果fieldValue是部门ID
            // return departmentCascadeService.isDepartmentInParent(actualTenantId, fieldValue, deptIdList);
            
            // 临时实现：简单的字符串匹配（需要替换为实际实现）
            return simpleStringMatch(fieldValue, deptIdList);
            
        } catch (Exception e) {
            log.error("Error in inDepartment function, fieldValue: {}, departmentIds: {}", 
                    fieldValue, departmentIds, e);
            return false;
        }
    }
    
    /**
     * 解析部门ID字符串为列表
     */
    private List<String> parseDepartmentIds(String departmentIds) {
        if (StringUtils.isBlank(departmentIds)) {
            return Lists.newArrayList();
        }
        
        // 移除前后的逗号（如果有）
        String cleanIds = departmentIds.trim();
        if (cleanIds.startsWith(",")) {
            cleanIds = cleanIds.substring(1);
        }
        if (cleanIds.endsWith(",")) {
            cleanIds = cleanIds.substring(0, cleanIds.length() - 1);
        }
        
        // 按逗号分割
        return Arrays.asList(cleanIds.split(","));
    }
    
    /**
     * 从上下文获取企业ID
     */
    private String getTenantIdFromContext(String tenantId) {
        // TODO: 这里需要根据实际情况从上下文中获取企业ID
        // 可能的方案：
        // 1. 从ThreadLocal中获取
        // 2. 从Spring Security Context中获取
        // 3. 从请求参数中获取
        
        if (StringUtils.isNotBlank(tenantId)) {
            return tenantId;
        }
        
        // 临时实现，返回默认值
        log.warn("getTenantIdFromContext not implemented, using default value");
        return "default_tenant";
    }
    
    /**
     * 临时的简单字符串匹配实现
     * 这个方法需要被实际的部门级联查询逻辑替换
     */
    private boolean simpleStringMatch(String fieldValue, List<String> deptIdList) {
        // 简单的包含判断，实际应该使用部门层级关系
        return deptIdList.contains(fieldValue);
    }
}
