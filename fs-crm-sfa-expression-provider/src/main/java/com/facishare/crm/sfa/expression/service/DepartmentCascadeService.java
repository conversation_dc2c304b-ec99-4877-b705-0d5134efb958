package com.facishare.crm.sfa.expression.service;

import java.util.List;

/**
 * 部门级联查询服务接口
 * 
 * <AUTHOR>
 * @date 2025/01/16
 */
public interface DepartmentCascadeService {
    
    /**
     * 获取指定部门及其所有子部门的ID列表
     * 
     * @param tenantId 企业ID
     * @param departmentIds 部门ID列表
     * @return 包含指定部门及其所有子部门的ID列表
     */
    List<String> getAllSubDepartmentIds(String tenantId, List<String> departmentIds);
    
    /**
     * 判断指定员工是否属于某个部门（包含子部门）
     * 
     * @param tenantId 企业ID
     * @param employeeId 员工ID
     * @param departmentIds 部门ID列表
     * @return 如果员工属于指定部门或其子部门，返回true
     */
    boolean isEmployeeInDepartment(String tenantId, String employeeId, List<String> departmentIds);
    
    /**
     * 判断指定部门是否属于某个部门（包含子部门）
     * 
     * @param tenantId 企业ID
     * @param targetDepartmentId 目标部门ID
     * @param parentDepartmentIds 父部门ID列表
     * @return 如果目标部门属于指定父部门或其子部门，返回true
     */
    boolean isDepartmentInParent(String tenantId, String targetDepartmentId, List<String> parentDepartmentIds);
}
