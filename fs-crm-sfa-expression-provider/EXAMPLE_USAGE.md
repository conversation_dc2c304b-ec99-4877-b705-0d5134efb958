# 部门级联过滤使用示例

## 调用流程说明

### 1. 前端请求示例

```json
{
  "filters": [
    {
      "field_name": "owner_department",
      "field_values": ["1000"],
      "operator": "IN",
      "is_cascade": true,
      "value_type": 0
    }
  ]
}
```

### 2. 后端处理流程

#### 步骤1：字段类型检查
在 `buildSingleExpressionString` 方法中：

```java
// 获取字段类型
String type = collect.get(0).getType();

switch (type) {
    // ... 其他类型
    case IFieldType.EMPLOYEE:
    case IFieldType.DEPARTMENT:  // 只有部门字段才会进入 getOrgExpression
        return getOrgExpression(filter, objectDescribe);
    // ... 其他类型
}
```

#### 步骤2：操作符判断
在 `getOrgExpression` 方法中：

```java
switch (filter.getOperator()) {
    // ... 其他操作符
    case IN:
    case LIKE:
        if (CollectionUtils.isEmpty(filter.getFieldValues())) {
            rst = NEVER_EQUAL_EXPRESSION;
            break;
        }
        // 检查是否为部门字段且需要包含子部门
        boolean isCascade = BooleanUtils.isTrue(filter.getIsCascade());
        if (isCascade && isDepartmentFieldType(filter, objectDescribe)) {
            // 只有同时满足以下条件才会使用级联表达式：
            // 1. is_cascade = true
            // 2. 字段类型为 IFieldType.DEPARTMENT
            rst = buildCascadeDepartmentExpression(filter);
        } else {
            // 原有逻辑
            fieldValue = String.join(",", filter.getFieldValues());
            fieldValue = String.format(",%s,", fieldValue);
            rst = String.format(" (!ISNULL(%s) && CONTAINS('%s',%s))", filedName,fieldValue, filedName);
        }
        break;
    // ... 其他操作符
}
```

#### 步骤3：字段类型验证
在 `isDepartmentFieldType` 方法中：

```java
private boolean isDepartmentFieldType(IFilter filter, IObjectDescribe objectDescribe) {
    String fieldName = filter.getFieldName();
    
    // 通过objectDescribe获取字段类型
    if (objectDescribe != null) {
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes().stream()
                .filter(x -> x.getApiName().equals(fieldName))
                .collect(Collectors.toList());
        
        if (!CollectionUtils.isEmpty(fieldDescribes)) {
            String fieldType = fieldDescribes.get(0).getType();
            // 只有部门字段类型才返回 true
            return IFieldType.DEPARTMENT.equals(fieldType);
        }
    }
    
    return false;
}
```

### 3. 不同场景的处理结果

#### 场景1：部门字段 + is_cascade: true
- **输入**：`owner_department IN ('1000')` + `is_cascade: true`
- **字段类型**：`IFieldType.DEPARTMENT`
- **输出表达式**：`(!ISNULL(owner_department) && INDEPARTMENT(owner_department,'1000'))`

#### 场景2：部门字段 + is_cascade: false
- **输入**：`owner_department IN ('1000')` + `is_cascade: false`
- **字段类型**：`IFieldType.DEPARTMENT`
- **输出表达式**：`(!ISNULL(owner_department) && CONTAINS(',1000,',owner_department))`

#### 场景3：员工字段 + is_cascade: true
- **输入**：`owner IN ('1000')` + `is_cascade: true`
- **字段类型**：`IFieldType.EMPLOYEE`
- **输出表达式**：`(!ISNULL(owner) && CONTAINS(',1000,',owner))` （忽略 is_cascade）

#### 场景4：文本字段 + is_cascade: true
- **输入**：`name LIKE 'test'` + `is_cascade: true`
- **字段类型**：`IFieldType.TEXT`
- **处理方式**：不会进入 `getOrgExpression`，直接使用 `getTextExpression`

### 4. 关键判断条件

级联表达式只有在以下**所有条件**同时满足时才会生效：

1. ✅ 字段类型为 `IFieldType.DEPARTMENT`
2. ✅ 操作符为 `IN` 或 `NIN`
3. ✅ `filter.getIsCascade()` 返回 `true`
4. ✅ `isDepartmentFieldType(filter, objectDescribe)` 返回 `true`

### 5. 测试验证

```java
// 测试部门字段级联
@Test
void testDepartmentFieldWithCascade() {
    // 设置字段类型为 DEPARTMENT
    when(fieldDescribe.getType()).thenReturn(IFieldType.DEPARTMENT);
    
    Filter filter = new Filter();
    filter.setFieldName("owner_department");
    filter.setOperator(Operator.IN);
    filter.setFieldValues(Lists.newArrayList("1000"));
    filter.setIsCascade(true); // 关键：设置级联标志
    
    // 应该生成包含 INDEPARTMENT 的表达式
    verify(expressionService).evaluate(contains("INDEPARTMENT"), eq(objectData));
}

// 测试员工字段不级联
@Test
void testEmployeeFieldIgnoresCascade() {
    // 设置字段类型为 EMPLOYEE
    when(fieldDescribe.getType()).thenReturn(IFieldType.EMPLOYEE);
    
    Filter filter = new Filter();
    filter.setFieldName("owner");
    filter.setOperator(Operator.IN);
    filter.setFieldValues(Lists.newArrayList("1000"));
    filter.setIsCascade(true); // 即使设置了级联标志
    
    // 也不应该生成 INDEPARTMENT 表达式
    verify(expressionService).evaluate(not(contains("INDEPARTMENT")), eq(objectData));
}
```

## 总结

这个实现确保了：
- 只有 `IFieldType.DEPARTMENT` 类型的字段才会考虑级联逻辑
- 级联逻辑只在 `IN` 和 `NIN` 操作符中生效
- `is_cascade` 参数只对部门字段有效，对其他字段类型无影响
- 保持了向后兼容性，不影响现有功能
