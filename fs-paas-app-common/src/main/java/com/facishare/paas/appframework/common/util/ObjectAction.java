package com.facishare.paas.appframework.common.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * TODO:替代CrmActionEnum
 * <p>
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/10/25.
 */
public enum ObjectAction {
    //配置文件使用的actionCode为BulkHangTag
    BULK_HANG_TAG("BulkHangTag", I18NKey.ACTION_BULK_HANG_TAG, "打标签", "BulkHangTag_button_default"),
    DELETE("Delete", I18NKey.action_delete, "删除", "Delete_button_default"),
    VIEW_DETAIL("View", I18NKey.action_view_detail, "查看详情"),
    VIEW_LIST("List", I18NKey.action_view_list, "查看列表"),
    CREATE("Add", I18NKey.action_add, "新建", "Add_button_default"),
    UPDATE("Edit", I18NKey.action_edit, "编辑", "Edit_button_default"),
    VIEW_ALL("ViewAll", I18NKey.action_view_all, "查看全部"),
    EDIT_ALL("EditAll", I18NKey.action_edit_all, "编辑全部"),
    BATCH_UPDATE("Batch_Edit", I18NKey.action_batch_edit, "批量编辑", "Batch_Edit_button_default"),
    CREATE_SAVE("Add_Save", I18NKey.action_add_save, "新建保存", "Add_Save_button_default"),
    UPDATE_SAVE("Edit_Save", I18NKey.action_edit_save, "编辑保存", "Edit_Save_button_default"),
    CREATE_SAVE_CONTINUE("Add_Save_Continue", I18NKey.action_add_submit_continue, "提交并新建", "Add_Save_Continue_button_default"),
    CREATE_SAVE_DRAFT("Add_Save_Draft", I18NKey.action_add_save_draft, "保存草稿", "Add_Save_Draft_button_default"),
    EDIT_SAVE_DRAFT("Edit_Save_Draft", I18NKey.action_add_save_draft, "保存草稿", "Edit_Save_Draft_button_default"),
    CREATE_SAVE_CREATE_CONTACT("Add_Save_Add_Contact", I18NKey.action_add_submit_add_obj, "提交并新建{0}", "Add_Save_Add_Contact_button_default") {
        @Override
        public String getActionLabel() {
            return getActionLabel(I18NExt.getOrDefault(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.CONTACT_API_NAME), "联系人"));
        }
    },
    CREATE_SAVE_CREATE_PROJECT_STAGE("Add_Save_Add_Project_Stage", I18NKey.action_add_submit_add_obj, "提交并新建{0}", "Add_Save_Add_Project_Stage_button_default") {
        @Override
        public String getActionLabel() {
            return getActionLabel(I18NExt.getOrDefault(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.PROJECT_STAGE_API_NAME), "阶段"));
        }
    },
    CREATE_SAVE_CREATE_DEPENDENCIES("Add_Save_Add_Dependencies", I18NKey.action_add_submit_add_obj, "提交并新建{0}", "Add_Save_Add_Dependencies_button_default") {
        @Override
        public String getActionLabel() {
            return getActionLabel(I18NExt.getOrDefault(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.DEPENDENCIES_API_NAME), "依赖关系"));
        }
    },
    CREATE_SAVE_CREATE_PROJECT_TASK("Add_Save_Add_Project_Task", I18NKey.action_add_submit_add_obj, "提交并新建{0}", "Add_Save_Add_Project_Task_button_default") {
        @Override
        public String getActionLabel() {
            return getActionLabel(I18NExt.getOrDefault(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.PROJECT_TASK_API_NAME), "任务"));
        }
    },
    CANCEL("Cancel", I18NKey.action_cancel, "取消", "Cancel_button_default"),
    SINGLE_CREATE("Single_Add", I18NKey.action_single_add, "添加一行", "Single_Add_button_default"),
    IMPORT_EXCEL("Import_Excel", I18NKey.action_import_excel, "从本地Excel导入", "Import_Excel_button_default"),
    BATCH_LOOKUP_CREATE("Batch_Lookup_Add", I18NKey.action_batch_lookup_add, "从{0}添加", "Batch_Lookup_Add_button"),
    TILE("Tile", I18NKey.ACTION_TILE, "平铺", "Tile_button_default"),
    INSERT("Insert", I18NKey.ACTION_INSERT, "插入", "Insert_button_default"),
    BATCH_IMPORT("Import", I18NKey.action_import, "导入"),
    BATCH_EXPORT("Export", I18NKey.action_export, "导出"),
    EXPORT_FILE("ExportFile", I18NKey.action_export_file, "导出图片/附件"),
    INVALID("Abolish", I18NKey.action_invalid, "作废", "Abolish_button_default"),
    RECOVER("Recover", I18NKey.action_recover, "恢复", "Recover_button_default"),
    PRINT("Print", I18NKey.action_print, "打印", "Print_button_default"),
    EXCEL_PRINT("ExcelPrint", "paas.udobj.action.print", "打印", "Excel_print_button_default"),
    CHANGE_OWNER("ChangeOwner", I18NKey.action_change_owner, "更换负责人", "ChangeOwner_button_default"),
    ADD_TEAM_MEMBER("AddTeamMember", I18NKey.ADD_EMPLOYEE, "添加相关团队成员", "AddTeamMember_button_default"),
    EDIT_TEAM_MEMBER("EditTeamMember", I18NKey.MODIFY_SALE, "编辑相关团队成员", "EditTeamMember_button_default"),
    DELETE_TEAM_MEMBER("DeleteTeamMember", I18NKey.REMOVE_EMPLOYEE, "移除相关团队成员", "DeleteTeamMember_button_default"),
    RELATE("Relate", I18NKey.action_relate, "关联"),
    BULK_RELATE("BulkRelate", I18NKey.action_bulk_relate, "关联"),
    BULK_DISRELATE("BulkDisRelate", I18NKey.action_bulk_disrelate, "解除关联"),
    BULK_DELETE("BulkDelete", I18NKey.action_bulk_delete, "删除"),
    BULK_INVALID("BulkInvalid", I18NKey.aaction_bulk_invalid, "作废"),
    BULK_RECOVER("BulkRecover", I18NKey.action_bulk_recover, "恢复"),
    START_BPM("StartBPM", I18NKey.action_start_bpm, "发起业务流程", "StartBPM_button_default"),
    VIEW_ENTIRE_BPM("ViewEntireBPM", I18NKey.action_view_whole_bpm, "查看业务流流程图"),
    STOP_BPM("StopBPM", I18NKey.action_stop_bpm, "终止业务流程"),
    CHANGE_BPM_APPROVER("ChangeBPMApprover", I18NKey.action_change_bpm_approver, "更换业务流处理人"),
    ADD_EVENT("AddEvent", I18NKey.SALE_RECORD, "销售记录"),
    SIGN_IN("SignIn", I18NKey.action_sign_in, "签到"),
    SIGN_OUT("SignOut", I18NKey.action_sign_out, "签退"),
    PAY("Pay", I18NKey.action_pay, "待收款"),
    INTELLIGENTFORM("IntelligentForm", I18NKey.action_intelligent_form, "智能表单"),
    DUPLICATECHECK("DuplicateCheckObj", I18NKey.REPETITION_CHECKER, "查重工具"),
    LOCK("Lock", I18NKey.action_lock, "锁定", "Lock_button_default"),
    UNLOCK("Unlock", I18NKey.action_unlock, "解锁", "Unlock_button_default"),
    MODIFYLOG_RECOVER("ModifyLog_Recover", I18NKey.action_modifylog_recover, "修改记录恢复"),
    SALE_RECORD("SaleRecord", I18NKey.action_sale_record, "销售记录"),
    SERVICE_RECORD("ServiceRecord", I18NKey.action_service_record, "服务记录"),
    DIAL("Dial", I18NKey.action_dial, "打电话"),
    SEND_MAIL("SendMail", I18NKey.action_send_mail, "发邮件", "SendMail_button_default"),
    DISCUSS("Discuss", I18NKey.action_discuss, "转发"),
    SCHEDULE("Schedule", I18NKey.action_schedule, "日程"),
    REMIND("Remind", I18NKey.action_remind, "提醒"),
    CLONE("Clone", I18NKey.COPY, "复制", "Clone_button_default"),
    VIEW_FEED_CARD("ViewFeedCard", I18NKey.action_view_feed_card, "查看销售记录"),
    CHANGE_PARTNER("ChangePartner", I18NKey.action_change_partner, "更换合作伙伴", "ChangePartner_button_default"),
    CHANGE_PARTNER_OWNER("ChangePartnerOwner", I18NKey.action_change_partner_owner, "更换外部负责人", "ChangePartnerOwner_button_default"),
    DELETE_PARTNER("DeletePartner", I18NKey.action_delete_partner, "移除合作伙伴", "DeletePartner_button_default"),
    START_STAGE_PROPELLOR("StartStagePropellor", I18NKey.start_stage_propellor, "发起阶段推进器", "StartStagePropellor_button_default"),
    UNKNOWN_ACTION("UnknownAction", I18NKey.action_unknow_action, "未知操作"),
    //6.3.3新增
    ADD_SPEC("AddSpec", I18NKey.action_add_spec, "增加规格"),
    RETURN("Return", I18NKey.action_return, "退回", "Return_button_default"),
    MOVE("Move", I18NKey.action_move, "转移", "Move_button_default"),
    CHOOSE("Choose", I18NKey.action_choose, "领取", "Choose_button_default"),
    ALLOCATE("Allocate", I18NKey.action_allocate, "分配", "Allocate_button_default"),
    TAKE_BACK("TakeBack", I18NKey.action_take_back, "收回", "TakeBack_button_default"),
    UPDATE_DEAL_STATUS("ChangeDealStatus", I18NKey.action_update_deal_status, "更改成交状态"),
    SAVE_TO_PHONE("SaveToPhone", I18NKey.action_save_to_phone, "保存到手机", "SaveToPhone_button_default"),
    CHANGE_SALE_ACTION("ChangeSaleAction", I18NKey.action_change_sale_action, "更换销售流程"),
    COLLECT("Collect", I18NKey.action_collect, "收款"),
    DEAL("Deal", I18NKey.action_deal, "转换"),
    CLOSE("Close", I18NKey.action_close, "无效", "Close_button_default"),
    FOLLOW_UP("FollowUp", I18NKey.action_follow_up, "跟进中", "FollowUp_button_default"),
    RECALL("Recall", I18NKey.action_recall, "撤回"),
    VIEW_LOGISTICS("ViewLogistics", I18NKey.action_view_logistics, "查看物流", "ViewLogistics_button_default"),
    CONFIRM_RECEIPT("ConfirmReceipt", I18NKey.action_confirm_receipt, "确认收货", "ConfirmReceipt_button_default"),
    CONFIRM_DELIVERY("ConfirmDelivery", I18NKey.action_confirm_delivery, "确认发货"),
    ADD_DELIVERY_NOTE("AddDeliveryNote", I18NKey.action_add_delivery_not, "创建发货单", "AddDeliveryNote_button_default"),
    CONFIRM_INBOUND("InboundConfirmed", I18NKey.action_confirm_inbound, "确认入库", "InboundConfirmed_button_default"),
    STATUS_OFF("StatusOff", I18NKey.action_status_off, "下架"),
    STATUS_ON("StatusOn", I18NKey.action_status_on, "上架"),
    VIEW_BEFORE_SALE_ACTION("ViewBeforeSaleAction", I18NKey.action_view_before_sale_action, "查看售前流程"),
    VIEW_AFTER_SALE_ACTION("ViewAfterSaleAction", I18NKey.action_view_after_sale_action, "查看售后流程"),
    CONFIRM("Confirm", I18NKey.action_confirm, "确认"),
    REJECT("Reject", I18NKey.action_reject, "驳回"),
    AUDIT("Audit", I18NKey.action_audit_customer, "确认"), //"确认"  客户报备（确认、驳回）客户报备
    CHANGE_AUDITOR("ChangeAuditor", I18NKey.action_change_auditor, "更换审核人"),//"更换审核人"  客户报备
    CHANGE_CONFIRMOR("ChangeConfirmor", I18NKey.action_change_confirmor, "更换确认人"),//"更换确认人"
    ADD_ATTACH("AddAttach", I18NKey.action_add_attach, "添加附件"), //"添加附件"
    CHANGE_STAGE("ChangeStage", I18NKey.action_change_stage, "阶段变更"), //"阶段变更"
    //6.5新增
    SCAN_CARD("ScanCard", I18NKey.action_scan_card, "扫名片"), //扫名片
    COMPLETE_SETTLEMENT("CompleteSettlement", I18NKey.complete_settlement, "完成结算"), //完成结算
    UPLOAD_DELETE_ATTACH("UploadDeleteAttach", I18NKey.action_attach_upload_delete, "附件上传/删除"), //附件上传/删除
    VIEW_ATTACH("ViewAttach", I18NKey.action_view_attach, "查看附件"), //附件查看
    IMPORT_FROM_ADDRESS_BOOK("ImportFromAddressBook", I18NKey.action_import_from_address_book, "通讯录导入"),
    MERGE("Merge", I18NKey.action_udobj_combine, "合并", "Merge_button_default"),
    MERGE_UI("MergeUI", I18NKey.action_udobj_combine_ui, "合并", "MergeUI_button_default"),
    BEFORE_SALE_ACTION("BeforeSaleAction", I18NKey.action_before_sale_action, "操作售前流程"),
    AFTER_SALE_ACTION("AfterSaleAction", I18NKey.action_after_sale_action, "操作售后流程"), //合并
    UPLOAD("Upload", I18NKey.action_upload, "上传"),
    //6.6.5
    CHANGE_STATES("ChangeStates", I18NKey.action_change_states, "状态变更", "ChangeStates_button_default"), //"状态变更"
    REMOVE("Remove", I18NKey.ACTION_REMOVE, "移除"), //"移除池"
    CONFIGURE_PRODUCT("ConfigureProduct", I18NKey.CONFIGURE_SBUPRODUCT_BUTTON, "配置产品组合"),
    CONFIGURE_COLLOCATION("ConfigureCollocation", I18NKey.CONFIGURE_COLLOCATION_BUTTON, "配置固定搭配"),
    CREATE_GROUP("CreateGroup", I18NKey.CONFIGURE_GROUP_BUTTON, "创建分组"),
    SYNC_PRODUCT_STRUCTURE("SyncProductStructure", I18NKey.CONFIGURE_SYNC_PRODUCT_STRUCTURE_BUTTON, "同步本产品结构"),
    SYNC_STRUCTURE_TO_OTHER("SyncStructureToOther", I18NKey.CONFIGURE_SYNC_STRUCTURE_TO_OTHER_BUTTON, "同步其他BOM的本产品"),
    SET_GROUP("SetGroup", I18NKey.CONFIGURE_SET_GROUP_BUTTON, "设置分组"),
    DEL_GROUP("DelGroup", I18NKey.CONFIGURE_DEL_GROUP_BUTTON, "删除分组"),
    COST_LIST("Cost_List", I18NKey.action_cost_list, "查看费用"),
    //6.8.0
    COLLECT_TO("Collect_To", I18NKey.action_collect_to, "归集", "CollectTo_button_default"),
    //6.9.0
    MarkMQL("MarkMQL", I18NKey.action_mark_mql, "标为市场认可线索MQL", "MarkMQL_button_default"),
    //7.0.0
    TRANSFER("Transfer", I18NKey.action_transfer, "转换保存", "Transfer_button_default"),
    TRANSFER_UI("TransferUI", I18NKey.action_transfer_ui, "转换", "TransferUI_button_default"),

    RECALCULATE("Recalculate", I18NKey.action_recalculate, "重新计算归因结果", "Recalculate_button_default"),

    //7.1.0 人员对象新增
    BulkPause("BulkPause", I18NKey.action_BulkPause, "禁止登录"),
    BulkAllow("BulkAllow", I18NKey.action_BulkAllow, "允许登录"),
    BulkResetPassword("BulkResetPassword", I18NKey.action_BulkResetPassword, "重置密码"),
    BulkActive("BulkActive", I18NKey.action_BulkActive, "激活提醒"),
    BulkModifyLeader("BulkModifyLeader", I18NKey.action_BulkModifyLeader, "修改汇报对象"),
    BulkStop("Stop", I18NKey.action_BulkStop, "停用", "BulkStop_button_default"),
    BulkResume("BulkResume", I18NKey.action_BulkResume, "启用"),
    BulkResetMainDepartment("BulkResetMainDepartment", I18NKey.action_BulkResetMainDepartment, "重设主属部门"),
    BulkResetViceDepartment("BulkResetViceDepartment", I18NKey.action_BulkResetViceDepartment, "重设附属部门"),
    BulkAddViceDepartment("BulkAddViceDepartment", I18NKey.action_BulkAddViceDepartment, "追加附属部门"),
    BulkDeleteViceDepartment("BulkDeleteViceDepartment", I18NKey.action_BulkDeleteViceDepartment, "删除附属部门"),

    //延期
    EXTEND_EXPIRETIME("ExtendExpireTime", I18NKey.action_extend_expireTime, "申请延期", "ExtendExpireTime_button_default"),
    //优先级
    PRIORITY("Priority", I18NKey.action_priority, "优先级", "Priority_button_default"),
    // 7.2.0
    TRANSFER_PARTNER("TransferPartner", I18NKey.TRANSFER_PARTNER, "转换合作伙伴", "TransferPartner_button_default"),
    // 转换新建(客户、联系人、商机、商机2.0、合作伙伴)
    TRANSFER_ADD("TransferAdd", I18NKey.TRANSFER_ADD, "转换新建"),
    //7.2.5新增查价工具
    PRICE_TOOL("PriceTool", I18NKey.PRICE_TOOL, "询价工具"),
    //7.3.0产品关联属性新增
    AssociateAttribute("AssociateAttribute", I18NKey.action_AssociateAttribute, "关联属性", "AssociateAttribute_button_default"),
    DisAssociateAttribute("DisAssociateAttribute", I18NKey.action_DisAssociateAttribute, "解除关联属性", "DisAssociateAttribute_button_default"),
    AssociateNonstandardAttribute("AssociateNonstandardAttribute", I18NKey.action_AssociateNonstandardAttribute, "关联非标属性", "AssociateNonstandardAttribute_button_default"),
    DisAssociateNonstandardAttribute("DisAssociateNonstandardAttribute", I18NKey.action_DisAssociateNonstandardAttribute, "解除关联非标属性", "DisAssociateNonstandardAttribute_button_default"),
    AttributeEnable("Enable", I18NKey.action_Enable, "启用", "AttributeEnable_button_default"),
    AttributeDisEnable("DisEnable", I18NKey.action_DisEnable, "禁用", "AttributeDisEnable_button_default"),
    CHANGE_STATUS("ChangeStatus", I18NKey.CHANGE_STATUS, "更新状态", "ChangeStatus_button_default"),
    REMOVE_OBJECT("RemoveObject", I18NKey.REMOVE_OBJECT, "移除成员", "RemoveObject_button_default"),
    FAST_PRODUCT_ASSOCIATE_PRICE_BOOK("FastProductAssociatePriceBook", I18NKey.FAST_PRODUCT_ASSOCIATE_PRICE_BOOK, "保存并添加到价目表", "FastProductAssociatePriceBook_Button_default"),
    EDIT_PRICE_BOOK_PRODUCT("EditPriceBookProduct", I18NKey.EDIT_PRICE_BOOK_PRODUCT, "编辑价目表明细", "EditPriceBookProduct_Button_default"),
    // 添加到市场活动
    AddCampaignMembers("AddCampaignMembers", I18NKey.action_AddCampaignMembers, "添加到{0}") {
        @Override
        public String getActionLabel() {
            return getActionLabel(I18NExt.getOrDefault(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.MARKETING_EVENT_API_NAME), "市场活动"));
        }
    },
    //7.4.5 设备绑定
    BulkRemoveBind("BulkRemoveBind", I18NKey.action_BulkRemoveBind, "解绑"),
    BulkAddDeviceWhiteList("BulkAddDeviceWhiteList", I18NKey.action_BulkAddDeviceWhiteList, "绑定白名单"),
    //7.4.5 管理后台员工列表
    BulkRemoveDeviceBind("BulkRemoveDeviceBind", I18NKey.action_BulkRemoveDeviceBind, "移除设备绑定白名单"),
    CHOOSE_MAIN_DATA("ChooseMainData", I18NKey.action_choose_main_data, "领取到本组织", "ChooseMainData_button_default"),
    ALLOCATE_MAIN_DATA("AllocateMainData", I18NKey.action_allocate_main_data, "分发到组织", "AllocateMainData_button_default"),
    PROCESS_LEADS("ProcessLeads", I18NKey.action_process_leads, "处理线索", "ProcessLeads_button_default"),
    //7.4.5 阶段推进器添加功能权限

    STAGE_MOVETO("StageMoveTo", I18NKey.STAGE_MOVETO, "阶段推进"),
    CHANGE_STAGE_CANDIDATEIDS("ChangeStageCandidateIds", I18NKey.CHANGE_STAGE_CANDIDATEIDS, "更换阶段任务处理人"),
    STAGE_BACKTO("StageBackTo", I18NKey.STAGE_BACKTO, "阶段回退"),
    STAGE_REACTIVATION("StageReactivation", I18NKey.STAGE_REACTIVATION, "阶段重新激活"),
    // 取消入账
    CANCEL_ENTRY("CancelEntry", I18NKey.CANCEL_ENTRY, "取消入账", "CancelEntry_button_default"),
    // 图片及附件下载
    PICTURE_ANNEX_DOWNLOAD("PictureAnnexDownload", I18NKey.ACTION_PICTURE_ANNEX_DOWNLOAD, "图片及附件下载"),

    //7.5.0 部门助理
    BulkModifyAssistant("BulkModifyAssistant", I18NKey.action_BulkModifyAssistant, "设置助理"),
    BulkModifyManager("BulkModifyManager", I18NKey.action_BulkModifyManager, "设置部门负责人"),

    //7.5.5 设为主，设为默认
    SET_MAIN("SetMain", I18NKey.SET_MAIN, "设为主地址", "SetMain_button_default"),
    SET_DEFAULT("SetDefault", I18NKey.SET_DEFAULT, "设为默认", "SetDefault_button_default"),
    VIEW_LOGISTICS_PREDEFINE("ViewLogisticsPredefine", I18NKey.VIEW_LOGISTICS_PREDEFINE, "ViewLogisticsPredefine", "ViewLogisticsPredefine_button_default"),
    CONFIRM_RECEIPT_PREDEFINE("ConfirmReceiptPredefine", I18NKey.CONFIRM_RECEIPT_PREDEFINE, "ConfirmReceiptPredefine", "ConfirmReceiptPredefine_button_default"),

    // 7.6.0
    VIEW_APPROVAL_INSTANCE_LOG("ViewApprovalInstanceLog", I18NKey.action_view_approval_instance_log, "查看审批意见"),
    VIEW_APPROVAL_CONFIG("ViewApprovalConfig", I18NKey.action_view_approval_config, "查看审批配置"),

    VIEW_BPM_INSTANCE_LOG("ViewBPMInstanceLog", I18NKey.view_bpm_instance_log, "查看业务流流程日志"),

    //日志点评
    Comment("Comment", I18NKey.Comment, "点评"),
    AsyncBulkComment("AsyncBulkComment", I18NKey.AsyncBulkComment, "批量点评"),

    // 服务通相关按钮
    CANCEL_DEVICE_PLAN_PUBLISH("CancelDevicePlanPublish", I18NKey.CANCEL_DEVICE_PLAN_PUBLISH, "取消发布", "CancelDevicePlanPublish_button_default"),
    DEVICE_PLAN_PUBLISH("DevicePlanPublish", I18NKey.DEVICE_PLAN_PUBLISH, "发布", "DevicePlanPublish_button_default"),
    DEVICE_PLAN_DETAIL_CREATE_CASES("DevicePlanDetailCreateCases", I18NKey.DEVICE_PLAN_DETAIL_CREATE_CASES, "生成工单", "DevicePlanDetailCreateCases_button_default"),
    CANCEL_PREVENTIVE_MAINTENANCE_PUBLISH("CancelPreventiveMaintenancePublish", I18NKey.CANCEL_PREVENTIVE_MAINTENANCE_PUBLISH, "取消发布", "CancelPreventiveMaintenancePublish_button_default"),
    PREVENTIVE_MAINTENANCE_PUBLISH("PreventiveMaintenancePublish", I18NKey.PREVENTIVE_MAINTENANCE_PUBLISH, "发布", "PreventiveMaintenancePublish_button_default"),
    PREVENTIVE_MAINTENANCE_CREATE_CASES("PreventiveMaintenanceCreateCases", I18NKey.PREVENTIVE_MAINTENANCE_CREATE_CASES, "生成工单", "PreventiveMaintenanceCreateCases_button_default"),
    CANCEL_CASES_BPM("CancelCasesBpm", I18NKey.CANCEL_CASES_BPM, "取消申请", "CancelCasesBpm_button_default"),
    CASES_MULTIPLE_CHECKINS("CasesMultipleCheckins", I18NKey.CASES_MULTIPLE_CHECKINS, "打卡", "CasesMultipleCheckins_button_default"),
    CASES_RELATED_INFO("CasesRelatedInfo", I18NKey.CASES_RELATED_INFO, "相关信息", "CasesRelatedInfo_button_default"),
    CASES_KNOWLEDGE_RECOMMEND("CasesKnowledgeRecommend", I18NKey.CASES_KNOWLEDGE_RECOMMEND, "服务知识推荐", "CasesKnowledgeRecommend_button_default"),
    RECEIVE_CASES("ReceiveCases", I18NKey.RECEIVE_CASES, "领取工单", "ReceiveCases_button_default"),
    VIEW_CASES_ENGINEER_BUSY_IDLE("ViewCasesEngineerBusyIdle", I18NKey.VIEW_CASES_ENGINEER_BUSY_IDLE, "查看工程师忙闲", "ViewCasesEngineerBusyIdle_button_default"),
    UPDATE_CASES_DEVICE_ADDRESS("UpdateCasesDeviceAddress", I18NKey.UPDATE_CASES_DEVICE_ADDRESS, "更新工单设备位置", "UpdateCasesDeviceAddress_button_default"),
    COMPLETE_FEE_SETTLEMENT_BILL("CompleteFeeSettlementBill", I18NKey.COMPLETE_FEE_SETTLEMENT_BILL, "完成结算", "CompleteFeeSettlementBill_button_default"),
    CONFIRM_RECEIVE_MATERIAL_APPLY("ConfirmReceiveMaterialApply", I18NKey.CONFIRM_RECEIVE_MATERIAL_APPLY, "确认领料申请", "ConfirmReceiveMaterialApply_button_default"),
    REJECT_RECEIVE_MATERIAL_APPLY("RejectReceiveMaterialApply", I18NKey.REJECT_RECEIVE_MATERIAL_APPLY, "驳回领料申请", "RejectReceiveMaterialApply_button_default"),
    SUBMIT_RECEIVE_MATERIAL_APPLY("SubmitReceiveMaterialApply", I18NKey.SUBMIT_RECEIVE_MATERIAL_APPLY, "提交领料申请", "SubmitReceiveMaterialApply_button_default"),
    CONFIRM_REFUND_MATERIAL_APPLY("ConfirmRefundMaterialApply", I18NKey.CONFIRM_REFUND_MATERIAL_APPLY, "确认退料申请", "ConfirmRefundMaterialApply_button_default"),
    REJECT_REFUND_MATERIAL_APPLY("RejectRefundMaterialApply", I18NKey.REJECT_REFUND_MATERIAL_APPLY, "驳回退料申请", "RejectRefundMaterialApply_button_default"),
    SUBMIT_REFUND_MATERIAL_APPLY("SubmitRefundMaterialApply", I18NKey.SUBMIT_REFUND_MATERIAL_APPLY, "提交退料申请", "SubmitRefundMaterialApply_button_default"),
    PREVIEW_KNOWLEDGE("PreviewKnowledge", I18NKey.PREVIEW_KNOWLEDGE, "预览", "PreviewKnowledge_button_default"),
    UPDATE_KNOWLEDGE_CATEGORY("UpdateKnowledgeCategory", I18NKey.UPDATE_KNOWLEDGE_CATEGORY, "更新分类", "UpdateKnowledgeCategory_button_default"),
    CASES_CALL_OUT("CasesCallOut", I18NKey.CASES_CALL_OUT, "呼出", "CasesCallOut_button_default"),
    APPRAISE_SKIP_RETURN_VISIT("AppraiseSkipReturnVisit", I18NKey.APPRAISE_SKIP_RETURN_VISIT, "跳过回访", "AppraiseSkipReturnVisit_button_default"),
    REASSIGN_CASES("ReassignCases", I18NKey.REASSIGN_CASES, "改派", "ReassignCases_button_default"),
    KNOWLEDGE_CANCEL_PUBLISH("KnowledgeCancelPublish", I18NKey.KNOWLEDGE_CANCEL_PUBLISH, "取消发布", "KnowledgeCancelPublish_button_default"),
    KNOWLEDGE_PUBLISH("KnowledgePublish", I18NKey.KNOWLEDGE_PUBLISH, "发布", "KnowledgePublish_button_default"),
    KNOWLEDGE_SAVE_AND_PUBLISH("KnowledgeSaveAndPublish", I18NKey.KNOWLEDGE_SAVE_AND_PUBLISH, "保存并发布", "KnowledgeSaveAndPublish_button_default"),
    ADD_ENGINEER("AddEngineer", I18NKey.ADD_ENGINEER, "添加工程师", "AddEngineer_button_default"),
    REASSIGN_ASSOCIATE_ENGINEER_CASES("ReassignAssociateEngineerCases", I18NKey.REASSIGN_ASSOCIATE_ENGINEER_CASES, "改派协同工程师", "ReassignAssociateEngineerCases_button_default"),
    CASES_CREATE_SERVICE_REPORT("CasesCreateServiceReport", I18NKey.CASES_CREATE_SERVICE_REPORT, "生成服务报告", "CasesCreateServiceReport_button_default"),
    PRINT_DEVICE_CODE("PrintDeviceCode", I18NKey.PRINT_DEVICE_CODE, "导出设备码", "PrintDeviceCode_button_default"),
    MERGE_PRINT_DEVICE_CODE("MergePrintDeviceCode", I18NKey.MERGE_PRINT_DEVICE_CODE, "合并导出设备码", "MergePrintDeviceCode_button_default"),
    DEVICE_REGISTER("DeviceRegister", I18NKey.DEVICE_REGISTER, "设备注册", "DeviceRegister_button_default"),
    SERVICE_REQUEST_FINISH("ServiceRequestFinish", I18NKey.SERVICE_REQUEST_FINISH, "完成服务请求", "ServiceRequestFinish_button_default"),
    SERVICE_AGREEMENT_CONFIRM_EFFECT("ServiceAgreementConfirmEffect", I18NKey.SERVICE_AGREEMENT_CONFIRM_EFFECT, "确认生效", "ServiceAgreementConfirmEffect_button_default"),
    PRINT_QR_CODE("PrintQrcode", I18NKey.PRINT_QR_CODE, "导出二维码", "PrintQrcode_button_default"),
    MERGE_PRINT_QR_CODE("MergePrintQrcode", I18NKey.MERGE_PRINT_QR_CODE, "合并导出二维码", "MergePrintQrcode_button_default"),
    QR_CODE_REGISTER("QrcodeRegister", I18NKey.QR_CODE_REGISTER, "空白码注册", "QrcodeRegister_button_default"),
    FAULT_KNOWLEDGE_RECOMMEND("FaultKnowledgeRecommend", I18NKey.FAULT_KNOWLEDGE_RECOMMEND, "故障知识推荐", "FaultKnowledgeRecommend_button_default"),
    ADD_SERVICE_PROVIDER("AddServiceProvider", I18NKey.ADD_SERVICE_PROVIDER, "添加服务商", "AddServiceProvider_button_default"),
    REASSIGN_CASES_ENGINEER("ReassignCasesEngineer", I18NKey.REASSIGN_CASES_ENGINEER, "改派工程师", "ReassignCasesEngineer_button_default"),
    REASSIGN_CASES_SERVICEGROUP("ReassignCasesServiceGroup", I18NKey.REASSIGN_CASES_SERVICEGROUP, "改派服务组", "ReassignCasesServiceGroup_button_default"),
    REASSIGN_CASES_SERVICEPROVIDER("ReassignCasesServiceProvider", I18NKey.REASSIGN_CASES_SERVICEPROVIDER, "改派服务商", "ReassignCasesServiceProvider_button_default"),
    CONSULT_QUESTION_RECORD_INEFFECTIVE("ConsultQuestionRecordIneffective", I18NKey.CONSULT_QUESTION_RECORD_INEFFECTIVE, "无效", "ConsultQuestionRecordIneffective_button_default"),
    CONSULT_QUESTION_RECORD_UNDETERMINED("ConsultQuestionRecordUndetermined", I18NKey.CONSULT_QUESTION_RECORD_UNDETERMINED, "待定", "ConsultQuestionRecordUndetermined_button_default"),
    CONSULT_QUESTION_RECORD_ADD_KNOWLEDGE("ConsultQuestionRecordAddKnowledge", I18NKey.CONSULT_QUESTION_RECORD_ADD_KNOWLEDGE, "新增知识", "ConsultQuestionRecordAddKnowledge_button_default"),
    START_MEETING("StartMeeting", I18NKey.START_MEETING, "发起会议", "StartMeeting_button_default"),
    SERVICE_KNOWLEDGE_SHARE("ServiceKnowledgeShare", I18NKey.SERVICE_KNOWLEDGE_SHARE, "分享", "ServiceKnowledgeShare_button_default"),
    ENTERPRISE_ORDER_COLLECTION("EnterpriseOrderCollection", I18NKey.ENTERPRISE_ORDER_COLLECTION, "收款", "EnterpriseOrderCollection_button_default"),
    ENTERPRISE_ORDER_REFUND("EnterpriseOrderRefund", I18NKey.ENTERPRISE_ORDER_REFUND, "退款", "EnterpriseOrderRefund_button_default"),
    SYNC_ENTERPRISE_ORDER_COLLECTION_RESULT("SyncEnterpriseOrderCollectionResult", I18NKey.SYNC_ENTERPRISE_ORDER_COLLECTION_RESULT, "同步收款结果", "SyncEnterpriseOrderCollectionResult_button_default"),
    VIEW_FAULT_TREE("ViewFaultTree", I18NKey.VIEW_FAULT_TREE, "查看故障树", "ViewFaultTree_button_default"),
    VOICE_TO_TEXT("VoiceToText", I18NKey.VOICE_TO_TEXT, "录音转文字", "VoiceToText_button_default"),
    WRITE_EXPRESS_BILL("WriteExpressBill", I18NKey.WRITE_EXPRESS_BILL, "填写快递单号", "WriteExpressBill_button_default"),
    CANCEL_REQUEST("CancelRequest", I18NKey.CANCEL_REQUEST, "取消申请", "CancelRequest_button_default"),
    SERVICE_REQUEST_VIEW_LOGISTICS("ServiceRequestViewLogistics", I18NKey.SERVICE_REQUEST_VIEW_LOGISTICS, "查看物流", "ServiceRequestViewLogistics_button_default"),
    SIGN_FOR_EXPRESS("SignForExpress", I18NKey.SIGN_FOR_EXPRESS, "签收", "SignForExpress_button_default"),
    VIEW_CHAT_RECORD("ViewChatRecord", I18NKey.VIEW_CHAT_RECORD, "查看聊天记录", "ViewChatRecord_button_default"),
    MAIL_REPLY("MailReply", I18NKey.MAIL_REPLY, "回复", "MailReply_button_default"),
    MAIL_REPLY_ALL("MailReplyAll", I18NKey.MAIL_REPLY_ALL, "回复全部", "MailReplyAll_button_default"),
    MAIL_FORWARD("MailForward", I18NKey.MAIL_FORWARD, "转发邮件", "MailForward_button_default"),
    MAIL_FORWARD_TO_MESSAGE("MailForwardToMessage", I18NKey.MAIL_FORWARD_TO_MESSAGE, "转发企信", "MailForwardToMessage_button_default"),
    MAIL_FORWARD_TO_ACTIVE_RECORD("MailForwardToActiveRecord", I18NKey.MAIL_FORWARD_TO_ACTIVE_RECORD, "转销售记录", "MailForwardToActiveRecord_button_default"),
    TRANSFER_SESSION("TransferSession", I18NKey.TRANSFER_SESSION, "转接", "TransferSession_button_default"),
    END_SESSION("EndSession", I18NKey.END_SESSION, "结束", "EndSession_button_default"),
    VIEW_LEAVE_MESSAGE("ViewLeaveMessage", I18NKey.VIEW_LEAVE_MESSAGE, "查看留言", "ViewLeaveMessage_button_default"),
    MAKE_UP_CHECKINS("MakeUpCheckins", I18NKey.MAKE_UP_CHECKINS, "全天补卡", "MakeUpCheckins_button_default"),
    QUERY_USER("QueryUser", I18NKey.QUERY_USER, "按用户查找", "QueryUser_button_default"),
    ADD_TO_KNOWLEDGE("AddToKnowledge", I18NKey.ADD_TO_KNOWLEDGE, "添加到知识库", "AddToKnowledge_button_default"),
    QAP_SAVE_AND_PUBLISH("QapSaveAndPublish", I18NKey.QAP_SAVE_AND_PUBLISH, "保存并发布", "QapSaveAndPublish_button_default"),
    QAP_PUBLISH("QapPublish", I18NKey.QAP_PUBLISH, "发布", "QapPublish_button_default"),
    QAP_AI_GENERATED("QapAiGenerated", I18NKey.QAP_AI_GENERATED, "AI生成", "QapAiGenerated_button_default"),

    RULE_DISABLE("RuleDisable", I18NKey.RULE_DISABLE, "禁用", "RuleDisable_button_default"),
    RULE_ENABLE("RuleEnable", I18NKey.RULE_ENABLE, "启用", "RuleEnable_button_default"),
    ADJUST_PRIORITY("AdjustPriority", I18NKey.ADJUST_PRIORITY, "调整优先级", "AdjustPriority_button_default"),
    EXECUTION_RULE("ExecutionRule", I18NKey.EXECUTION_RULE, "生成", "ExecutionRule_button_default"),

    ADVANCED_FORMULAS("AddAdvancedFormulas", I18NKey.ADVANCED_FORMULAS, "关联高级公式"),

    CREATE_BOM("CreateBom", I18NKey.CREATE_BOM, "生成标准BOM"),
    SET_ATTR_GROUP("SetAttrGroup", I18NKey.SET_ATTR_GROUP, "设置属性分组", "SetAttrGroup_button_default"),
    SET_ATTR_RANGE("SetAttrRange", I18NKey.SET_ATTR_RANGE, "设置属性范围", "SetAttrRange_button_default"),
    /**
     * 进销存 预置按钮
     */
    CONFIRM_RECEIVING("ConfirmReceiving", I18NKey.CONFIRM_RECEIVING, "确认签收", "ConfirmReceiving_button_default"),


    UPDATE_GDPR("UpdateGdpr", I18NKey.GDPR_BUTTON_DEFAULT, "更新法律基础", "Gdpr_button_default"),
    ENTER_ACCOUNT("EnterAccount", I18NKey.ENTER_ACCOUNT, "入账", "EnterAccount_button_default"),
    /**
     * 电子签签署按钮
     */
    SIGN_FILE("SignFile", I18NKey.SIGN_FILE, "签署", "SignFile_button_default"),
    //电子签批量签署按钮
    BULK_E_SIGN_FILE("BulkESignFile", I18NKey.BULK_E_SIGN_FILE, "签署", "BulkESignFile_button_default"),
    // 775
    BUDGET_TRANSFER("BudgetTransfer", I18NKey.ACTION_BUDGET_TRANSFER, "预算调拨", "BudgetTransfer_button_default"),
    BUDGET_TRANSFER_IN("BudgetTransferIn", I18NKey.ACTION_BUDGET_TRANSFER_IN, "预算追加", "BudgetTransferIn_button_default"),
    BUDGET_TRANSFER_OUT("BudgetTransferOut", I18NKey.ACTION_BUDGET_TRANSFER_OUT, "预算扣减", "BudgetTransferOut_button_default"),

    /**
     * 775 添加到价目表
     */
    ADD_TO_PRICEBOOK("AddToPriceBook", I18NKey.ACTION_ADD_TO_PRICEBOOK, "添加到价目表", "AddToPriceBook_button_default"),
    START_ENTERPRISE_RELATION("StartEnterpriseRelation", I18NKey.START_ENTERPRISE_RELATION, "启用互联企业", "StartEnterpriseRelation_button_default"),
    STOP_ENTERPRISE_RELATION("StopEnterpriseRelation", I18NKey.STOP_ENTERPRISE_RELATION, "停用互联企业", "StopEnterpriseRelation_button_default"),
    START_PUBLIC_EMPLOYEE("StartPublicEmployee", I18NKey.START_PUBLIC_EMPLOYEE, "启用互联用户", "StartPublicEmployee_button_default"),
    STOP_PUBLIC_EMPLOYEE("StopPublicEmployee", I18NKey.STOP_PUBLIC_EMPLOYEE, "停用互联用户", "StopPublicEmployee_button_default"),

    //780
    GANTT_VIEW("Gantt_view", I18NKey.GANTT_VIEW, "甘特视图", "Gantt_view_button_default"),
    KANBAN_VIEW("kanban_view", I18NKey.KANBAN_VIEW, "看板视图", "kanban_view_button_default"),
    RESOURCE_VIEW("Resource_view", I18NKey.RESOURCE_VIEW, "资源视图", "resource_view_button_default"),
    DNB_COMMERCIAL_INFOR_QUERY("DnbCommercialInforQuery", I18NKey.DNB_COMMERCIAL_INFOR_QUERY, "邓白氏工商查询"),
    EYE_COMMERCIAL_INFOR_QUERY("EyeCommercialInforQuery", I18NKey.EYE_COMMERCIAL_INFOR_QUERY, "天眼查工商查询"),

    //790
    CREATE_CHECKINS("CreateCheckins", I18NKey.CREATE_CHECKINS, "新建外勤", "CreateCheckins_button_default"),
    CLOSE_TPM_ACTIVITY("CloseTPMActivity", I18NKey.CLOSE_TPM_ACTIVITY, "结案", "CloseTPMActivity_button_default"),
    TPM_PROOF_RANDOM_AUDIT("TPMProofRandomAudit", I18NKey.TPM_PROOF_RANDOM_AUDIT, "抽检", "TPMProofRandomAudit_button_default"),
    GOODS_DELIVERY("GoodsDelivery", I18NKey.GOODS_DELIVERY, "发货", "GoodsDelivery_button_default"),
    STORE_COPY("StoreCopy", I18NKey.STORE_COPY, "批量门店复制", "StoreCopy_button_default"),
    STORE_TRANSFER("StoreTransfer", I18NKey.STORE_TRANSFER, "批量门店转移", "StoreTransfer_button_default"),

    // 795
    // 立即付款
    PAY_INSTANTLY("PayInstantly", I18NKey.PAY_INSTANTLY, "立即付款", "PayInstantly_button_default"),
    // 确认收货2
    CONFIRM_RECEIPT2("ConfirmReceipt2", I18NKey.CONFIRM_RECEIPT2, "确认收货2", "ConfirmReceipt2_button_default"),
    // 再次下单
    BUY_AGAIN("BuyAgain", I18NKey.BUY_AGAIN, "再次下单", "BuyAgain_button_default"),
    // 加入门店
    JOINROUT("Joinrout", I18NKey.JOINROUT, "加入路线", "Joinrout_button_default"),

    // 800
    //二维码收款
    QR_CODE("QR_code", I18NKey.QR_CODE, "二维码收款", "Qr_code_button_default"),
    // 在线支付
    ONLINE_PAY("Online_pay", I18NKey.ONLINE_PAY, "在线支付", "Online_pay_button_default"),

    //805
    Init("Init", I18NKey.ACTION_INIT, "初始化", "Init_button_default"),


    // 810
    SALES_ORDER_COST_ASSIGN("SalesOrderCostAssign", I18NKey.SALES_ORDER_COST_ASSIGN, "费用签收确认", "SalesOrderCostAssign_button_default"),

    REPORT_ANALYSIS("ReportAnalysis", I18NKey.REPORT_ANALYSIS, "计算分析结果", "ReportAnalysis_button_default"),
    //RFM重做评估
    RE_ANALYSIS_RFM("ReAnalysisRFM", I18NKey.RE_ANALYSIS_RFM, "重做RFM评估", "ReAnalysisRFM_button_default"),

    TRANSFER_PROCUREMENT("TransferProcurement", I18NKey.TRANSFER_PROCUREMENT, "转换招投标", "TransferProcurement_button_default"),

    ALLOCATE_PROCUREMENT("AllocateProcurement", I18NKey.ALLOCATE_PROCUREMENT, "领取", "AllocateProcurement_button_default"),
    BULK_CHOOSE_PROCUREMENT("BulkChooseProcurement", I18NKey.BULK_CHOOSE_PROCUREMENT, "批量领取", "BulkChooseProcurement_button_default"),

    RELATED_PROCUREMENT("RelatedProcurement", I18NKey.RELATED_PROCUREMENT, "获取最新关联公告", "RelatedProcurement_button_default"),

    INHERIT("Inherit", I18NKey.INHERIT_WECHAT, "继承", "Inherit_button_default"),
    FRIEND_INHERIT("FriendInherit", I18NKey.FRIEND_INHERIT_WECHAT, "好友继承", "FriendInherit_button_default"),
    GROUP_INHERIT("GroupInherit", I18NKey.GROUP_INHERIT_WECHAT, "群主继承", "GroupInherit_button_default"),

    RELATED_EMPLOYEE("RelatedEmployee", I18NKey.RELATED_EMPLOYEE, "关联员工", "RelatedEmployee_button_default"),
    JUDGE_QI_RESULT("JudgeQIResult", I18NKey.JUDGE_QIRESULT, "判定质检结果", "JudgeQIResult_button_default"),

    AGREEMENT_STORE_CONFIRM("AgreementStoreConfirm", I18NKey.ACTION_AGREEMENT_STORE_CONFIRM, "确认协议", "AgreementStoreConfirm_button_default"),

    BUDGET_CARRY_OVER("BudgetCarryOver", I18NKey.BUDGET_CARRY_OVER, "预算结转", "BudgetCarryOver_button_default"),
    BATCH_MANAGE_PICTURE("BatchManagePicture", I18NKey.BATCH_MANAGE_PICTURE, "批量管理图片", "BatchManagePicture_button_default"),

    //815
    BUDGET_TAKE_APART("BudgetTakeApart", I18NKey.BUDGET_TAKE_APART, "预算拆解", "BudgetTakeApart_button_default"),
    COST_WRITE_OFF("CostWriteOff", I18NKey.COST_WRITE_OFF, "门店费用核销", "CostWriteOff_button_default"),
    AREA_BATCH_EDIT("AreaBatchEdit", I18NKey.AREA_BATCH_EDIT, "编辑", "Batch_Edit_button_default"),
    AREA_BATCH_CHECK("AreaBatchCheck", I18NKey.AREA_BATCH_CHECK, "批量查看", "Batch_Check_button_default"),
    AREA_BATCH_ADD("AreaBatchAdd", I18NKey.AREA_BATCH_ADD, "批量新建", "Batch_Add_button_default"),
    AREA_MERGE("AreaMerge", I18NKey.AREA_MERGE, "合并", "Area_Merge_button_default"),

    // 820
    BALANCE_REDUCE("BalanceReduce", I18NKey.BALANCE_REDUCE, "余额扣减", "BalanceReduce_button_default"),
    MULTI_ACCOUNT_REDUCE("MultiAccountReduce", I18NKey.MULTI_ACCOUNT_REDUCE, "多账户扣减", "MultiAccountReduce_button_default"),
    CALL_OUT("CallOut", I18NKey.CALL_OUT, "外呼", "CallOut_button_default"),
    STATISTIC_TABLE_REFRESH("StatisticTableRefresh", I18NKey.STATISTIC_TABLE_REFRESH, "刷新预算汇总", "BudgetStatisticTableRefresh_button_default"),
    ONE_MORE_ORDER("OneMoreOrder", I18NKey.ACTION_COPY, "再来一单", "OneMoreOrder_button_default"),
    ENABLE_BUDGET("EnableBudget", I18NKey.ENABLE_BUDGET, "启用预算表", "EnableBudget_button_default"),
    // 8.2.5
    SEND_AS_TEMPLATE("SendAsTemplate", I18NKey.SEND_AS_TEMPLATE, "以模板发送"),
    SEND_ATTACHMENT("SendAttachment", I18NKey.SEND_ATTACHMENT, "发送附件"),
    RESET_PUBLIC_EMPLOYEE_PASSWORD("ResetPublicEmployeePassword", I18NKey.RESET_PUBLIC_EMPLOYEE_PASSWORD, "重置密码", "ResetPublicEmployeePassword_button_default"),
    //830
    ADD_SUPERIOR("AddSuperior", I18NKey.ADD_SUPERIOR, "添加上级", "AddSuperior_button_default"),
    ADD_SUBORDINATE("AddSubordinate", I18NKey.ADD_SUBORDINATE, "添加下级", "AddSubordinate_button_default"),
    RELIEVE_SUPERIOR("RelieveSuperior", I18NKey.RELIEVE_SUPERIOR, "解除上级关系", "RelieveSuperior_button_default"),
    ENTERPRISE_WECHAT_IMPORT("EnterpriseWechatImport", I18NKey.ENTERPRISE_WECHAT_IMPORT, "企微导入"),
    ENTERPRISE_WECHAT_ADD("EnterpriseWechatAdd", I18NKey.ENTERPRISE_WECHAT_ADD, "企微新建"),
    ENTERPRISE_WECHAT_RELEVANCE("EnterpriseWechatRelevance", I18NKey.ENTERPRISE_WECHAT_RELEVANCE, "企微一键关联"),
    Print_Receipt("PrintReceipt", I18NKey.print_receipt, "打印小票", "PrintReceipt_button_default"),
    Conformance_Statements("ConformanceStatements", I18NKey.conformance_statements, "确认对账", "ConformanceStatements_button_default"),
    TRANSFER_ACCOUNT("button_transferAccount__c", I18NKey.transferAccount, "转客户", "button_transferAccount__c"),
    TRANSFER_PARTNER_PRO("button_transferPartnerPro__c", I18NKey.transferPartnerPro, "转合作伙伴", "button_transferPartnerPro__c"),
    TRANSFER_COMPETITOR("button_transferCompetitor__c", I18NKey.transferCompetitor, "转竞争对手", "button_transferCompetitor__c"),
    BUDGET_ACCRUAL("BudgetAccrual", I18NKey.BUDGET_ACCRUAL, "入账", "BudgetAccrual_button_default"),
    BUDGET_CLOSURE("BudgetClosure", I18NKey.BUDGET_CLOSURE, "结案", "BudgetClosure_button_default"),
    GOODS_RECEIVED_NOTE_GENERATED("GoodsReceivedNoteGenerated", I18NKey.GOODS_RECEIVED_NOTE_GENERATED, "生成盘盈入库单", "GoodsReceivedNoteGenerated_button_default"),
    OUTBOUND_NOTE_GENERATED("OutboundNoteGenerated", I18NKey.OUTBOUND_NOTE_GENERATED, "生成盘亏出库单", "OutboundNoteGenerated_button_default"),
    IMPORT_UPDATE("ImportUpdate", I18NKey.IMPORT_UPDATE, "导入更新", "ImportUpdate_button_default"),
    RETURN_RECEIPT("ReturnReceipt", I18NKey.RETURN_RECEIPT, "退运签收入库", "ReturnReceipt_button_default"),
    //开发课程演示用
    TEST_CAR_SELL("TestCarSell", "", "卖出", "TestCarSell_button_default"),
    //840审批流和业务流新增function code
    APPROVAL_AFTER_ERROR_RETRY("ApprovalAfterErrorRetry", I18NKey.APPROVAL_AFTER_ERROR_RETRY, "审批流后动作异常重试"),
    APPROVAL_AFTER_ERROR_IGNORE("ApprovalAfterErrorIgnore", I18NKey.APPROVAL_AFTER_ERROR_IGNORE, "审批流后动作异常忽略"),
    BPM_AFTER_ERROR_RETRY("BpmAfterErrorRetry", I18NKey.BPM_AFTER_ERROR_RETRY, "业务流后动作异常重试"),
    BPM_AFTER_ERROR_IGNORE("BpmAfterErrorIgnore", I18NKey.BPM_AFTER_ERROR_IGNORE, "业务流后动作异常忽略"),
    //840
    INITIATE_RECONCILIATION("InitiateReconciliation", I18NKey.INITIATE_RECONCILIATION, "发起对账", "InitiateReconciliation_button_default"),
    CONFIRM_RECONCILIATION("ConfirmReconciliation", I18NKey.CONFIRM_RECONCILIATION, "确认对账", "ConfirmReconciliation_button_default"),
    // 拆解重试
    DISASSEMBLY_RETRY("DisassemblyRetry", I18NKey.DISASSEMBLY_RETRY, "拆解重试", "BudgetDisassemblyRetry_button_default"),
    // 解冻重试
    DisassemblyUnFrozenRetry("DisassemblyUnFrozenRetry", I18NKey.DISASSEMBLY_UN_FROZEN_RETRY, "解冻重试", "BudgetDisassemblyUnFrozen_button_default"),

    // 线索详情获取回访中间号按钮
    REVISIT_VIRTUAL_PHONE("RevisitVirtualPhone", I18NKey.REVISIT_VIRTUAL_PHONE, "获取回访中间号", "RevisitVirtualPhone_button_default"),
    BUDGET_CARRY_FORWARD_RETRY("BudgetCarryForwardRetry", "paas.udobj.action.budget_carry_forward_retry", "重试", "BudgetCarryForwardRetry_button_default"),
    // 860 变更单按钮
    CHANGE("Change", I18NKey.CHANGE_ACTION, "变更", "Change_button_default"),
    EFFECTIVE("Effective", I18NKey.EFFECTIVE_ACTION, "生效", "Effective_button_default"),
    CLOSE_SALES_ORDER("CloseSalesOrder", I18NKey.CLOSE_SALES_ORDER, "订单关闭", "CloseSalesOrder_button_default"),
    CONFIRM_ORDER("ConfirmOrder", I18NKey.CONFIRM_ORDER, "确认订单", "ConfirmOrder_button_default"),
    CANCELLATION_ORDER("CancellationOrder", I18NKey.CANCELLATION_ORDER, "取消订单", "CancellationOrder_button_default"),
    TERMINATE_ORDER("TerminateOrder", I18NKey.TERMINATE_ORDER, "终止订单", "TerminateOrder_button_default"),
    CLOSE_ORDER("CloseOrder", I18NKey.CLOSE_ORDER, "订单关闭", "CloseOrder_button_default"),
    // 更换审批流处理人
    CHANGE_APPROVAL_CANDIDATE_IDS("ChangeApprovalCandidateIds", I18NKey.CHANGE_APPROVAL_CANDIDATE_IDS, "更换审批流处理人"),
    // 860 快消
    PROMOTER_RESIGNED("PromoterResigned", I18NKey.PROMOTER_RESIGNED, "离职", "PromoterResigned_button_default"),
    PROMOTER_IN_POSITION("PromoterInPosition", I18NKey.PROMOTER_IN_POSITION, "入职", "PromoterInPosition_button_default"),
    // 870 快消 9月迭代
    PROMOTER_AGREE("PromoterAgree", I18NKey.PROMOTER_AGREE, "审核通过", "PromoterAgree_button_default"),
    PROMOTER_DISAGREE("PromoterDisagree", I18NKey.PROMOTER_DISAGREE, "审核驳回", "PromoterDisagree_button_default"),
    //890 快消 二维码邀请
    PROMOTER_QRCODE_INVITE("PromoterQRCodeInvite", I18NKey.PROMOTER_QRCODE_INVITE, "二维码邀请", "PromoterQRCodeInvite_button_default"),
    //960 快消 2506迭代 工资相关
    //工资条下发
    DISTRIBUTE("Distribute", I18NKey.DISTRIBUTE, "下发工资条", "Distribute_button_default"),
    //修正数据
    CORRECTION("Correction", I18NKey.CORRECTION, "修正数据", "Correction_button_default"),
    //更新工资数据
    UPDATE_SALARY_DATA("UpdateSalaryData", I18NKey.UPDATE_SALARY_DATA, "更新工资数据", "UpdateSalaryData_button_default"),
    // 865
    JUMP_SHOP("JumpShop", I18NKey.JUMP_SHOP, "跳店", "JumpShop_button_default"),
    CREATE_OUT_PLAN("CreateOutPlan", I18NKey.CREAT_OUT_PLAN, "新建外部计划", "CreateOutPlan_button_default"),

    // 870
    GENERATE_TREE_RELATION("GenerateTreeRelation", I18NKey.GENERATE_TREE_RELATION, "创建客户树", "GenerateTreeRelation_button_default"),
    ADD_SPARE_PART_DELIVERY("AddSparePartDelivery", I18NKey.ADD_SPARE_PART_DELIVERY, "创建备件发运单", "AddSparePartDelivery_button_default"),

    // 870 推拉单
    TRANSFORM("Transform", I18NKey.TRANSFORM, "转换", "Transform_button_default"),
    REFERENCE_CREATE("ReferenceCreate", I18NKey.REFERENCE_CREATE, "拉单", "ReferenceCreate_button_default"),
    NODE_CHANGE("NodeChange", I18NKey.NODE_CHANGE, "节点变更", "NodeChange_button_default"),
    SET_ROOT("SetRoot", I18NKey.SET_ROOT, "成为根节点", "SetRoot_button_default"),

    QUERY_EQUITY_RELATIONSHIP("QueryEquityRelationship", I18NKey.QUERY_EQUITY_RELATIONSHIP, "获取股权关系", "QueryEquityRelationship_button_default"), //"状态变更"
    BULK_HIDE_ANNOUNCE("BulkHideAnnounce", I18NKey.BULK_HIDE_ANNOUNCE, "批量不显示公告"),
    BULK_SHOW_ANNOUNCE("BulkShowAnnounce", I18NKey.BULK_SHOW_ANNOUNCE, "批量显示公告"),

    // 875 蚂蚁风险大脑
    BLACKLIST_MARK("BlacklistMark", I18NKey.BLACKLIST_MARK, "标记黑名单", "BlacklistMark_button_default"),
    BLACKLIST_CANCEL("BlacklistCancel", I18NKey.BLACKLIST_CANCEL, "取消黑名单", "BlacklistCancel_button_default"),
    RISK_MONITOR_ENABLE("RiskMonitorEnable", I18NKey.RISK_MONITOR_ENABLE, "添加企业风险监控", "RiskMonitorEnable_button_default"),
    RISK_MONITOR_DISABLE("RiskMonitorDisable", I18NKey.RISK_MONITOR_DISABLE, "取消企业风险监控", "RiskMonitorDisable_button_default"),
    RISK_PORTRAIT_ENABLE("RiskPortraitEnable", I18NKey.RISK_PORTRAIT_ENABLE, "开启风险画像", "RiskPortraitEnable_button_default"),
    // 数据同步
    CRM_SYNC_DATA("SyncData", I18NKey.ACTION_CRM_SYNCDATA, "数据同步", "CrmSyncData_button_default"),
    EXPORT_GANTT_CHART("ExportGanttChart", I18NKey.EXPORT_GANTT_CHART, "导出甘特图", "ExportGanttChart_button_default"),
    GENERATE_PROJECT_BUDGET("GenerateProjectBudget", I18NKey.GENERATE_PROJECT_BUDGET, "编制预算", "GenerateProjectBudget_button_default"),
    UPDATE_PERSON_BUDGET("UpdatePersonBudget", I18NKey.UPDATE_PERSON_BUDGET, "更新人力预算", "UpdatePersonBudget_button_default"),
    // 入账
    REFUND("Refund", I18NKey.REFUND, "入账", "Refund_button_default"),

    //880关注/取消关注
    FOLLOW("Follow", I18NKey.FOLLOW, "关注", "Follow_button_default"),
    UNFOLLOW("Unfollow", I18NKey.UNFOLLOW, "取消关注", "Unfollow_button_default"),
    OnlinePayment("OnlinePayment", I18NKey.ONLINE_PAYMENT, "在线支付", "OnlinePayment_button_default"),

    //880穿透工作台
    Deactivate("Deactivate", I18NKey.Deactivate, "停用", "Deactivate_button_default"),
    VIEW_RULE("ViewRule", I18NKey.VIEW_RULE, "查看规则", "ViewRule_button_default"),

    // 付款
    PurchasePay("PurchasePay", I18NKey.PURCHASE_PAY, "付款", "PurchasePay_button_default"),
    //890
    RELATED("Related", I18NKey.action_relate, "关联", "Related_button_default"),
    // 解除绑定
    UNBIND_ACCOUNT("UnBindAccount", I18NKey.UNBIND_ACCOUNT, "解除绑定", "UnBindAccount_button_default"),

    // 895
    ACTIVITY_PROOF("ActivityProof", I18NKey.ACTIVITY_PROOF, "活动举证", "ActivityProof_button_default"),
    RED_ACCOUNTS_RECEIVABLE_CREATE("RedAccountsReceivableCreate", I18NKey.RED_ACCOUNTS_RECEIVABLE_CREATE, "生成红字应收", "RedAccountsReceivableCreate_button_default"),

    //900
    EXCHANGE_OUTBOUND("ExchangeOutbound", I18NKey.EXCHANGE_OUTBOUND, "创建换货出库单", "ExchangeOutbound_button_default"),
    ADJUST_AFTER_COUNTING("AdjustAfterCounting", I18NKey.ADJUST_AFTER_COUNTING, "个人库明细调整", "AdjustAfterCounting_button_default"),
    ADD_PURCHASE_GOODS_RECEIVED_NOTE("AddPurchaseGoodsReceivedNote", I18NKey.ADD_PURCHASE_GOODS_RECEIVED_NOTE, "创建入库单", "AddPurchaseGoodsReceivedNote_button_default"),
    CLOSE_ACTIVITY_AGREEMENT("CloseActivityAgreement", I18NKey.CLOSE_ACTIVITY_AGREEMENT, "终止协议", "CloseActivityAgreement_button_default"),
    UPDATE_RISK_INFORMATION("UpdateRiskInformation", I18NKey.UPDATE_RISK_INFORMATION, "更新风险信息", "UpdateRiskInformation_button_default"),

    //910会员
    FALLBACK("Fallback", I18NKey.FALLBACK, "回退", "Fallback_button_default"),
    CANCEL_LOYALTY("CancelLoyalty", I18NKey.CANCEL_LOYALTY, "取消会员计划", "CancelLoyalty_button_default"),
    POOL_CHANGE_POINTS("PoolChangePoints", I18NKey.POOL_CHANGE_POINTS, "增加/减少积分", "PoolChangePoints_button_default"),
    MEMBER_CHANGE_POINTS("MemberChangePoints", I18NKey.MEMBER_CHANGE_POINTS, "增加/减少积分", "MemberChangePoints_button_default"),
    MEMBER_TRANSFER_POINTS("MemberTransferPoints", I18NKey.MEMBER_TRANSFER_POINTS, "积分转出", "MemberTransferPoints_button_default"),
    MEMBER_MERGE("MemberMerge", I18NKey.MEMBER_MERGE, "会员合并", "MemberMerge_button_default"),
    MEMBER_SET_LEVEL("MemberSetLevel", I18NKey.MEMBER_SET_LEVEL, "设置等级", "MemberSetLevel_button_default"),
    MEMBER_WORKING_HOURS_FILL("MemberWorkingHoursFill", I18NKey.MEMBER_WORKING_HOURS_FILL, "成员工时填报", "MemberWorkingHoursFill_button_default"),
    STOCK_QUANTITY_REFRESH("StockQuantityRefresh", I18NKey.STOCK_QUANTITY_REFRESH, "库存量刷新", "StockQuantityRefresh_button_default"),
    DEVICE_BOM_CONFIG("DeviceBomConfig", I18NKey.DEVICE_BOM_CONFIG, "配置OBOM", "DeviceBomConfig_button_default"),
    ASSET_BORROWING_RETURN("AssetBorrowingReturn", I18NKey.ASSET_BORROWING_RETURN, "归还", "AssetBorrowingReturn_button_default"),
    ASSET_BORROWING_EXTENSION("AssetBorrowingExtension", I18NKey.ASSET_BORROWING_EXTENSION, "延期", "AssetBorrowingExtension_button_default"),

    //920 银行流水
    IDENTIFY_ACCOUNT("IdentifyAccount", I18NKey.IDENTIFY_ACCOUNT, "识别客户", "IdentifyAccount_button_default"),
    CONFIRM_RECEIPT_PAYMENT("ConfirmReceiptPayment", I18NKey.CONFIRM_RECEIPT_PAYMENT, "确认", "ConfirmReceiptPayment_button_default"),
    // 冻结库存调整
    FREEZE_INVENTORY_ADJUSTMENT("FreezeInventoryAdjustment", I18NKey.FREEZE_INVENTORY_ADJUSTMENT, "冻结库存调整", "FreezeInventoryAdjustment_button_default"),
    // 确认
    INSPECTION_RECORD_OK("InspectionRecordOk", I18NKey.INSPECTION_RECORD_OK, "确认", "InspectionRecordOk_button_default"),
    // 申诉
    INSPECTION_RECORD_APPEAL("InspectionRecordAppeal", I18NKey.INSPECTION_RECORD_APPEAL, "申诉", "InspectionRecordAppeal_button_default"),
    // 新建渠道商家
    CREATE_PARTNER("CreatePartner", I18NKey.CREATE_PARTNER, "新建渠道商家", "CreatePartner_button_default"),
    // 同步价目表
    SYNC_TO_PRICE_BOOK("SyncToPriceBook", I18NKey.SYNC_TO_PRICE_BOOK, "同步价目表", "SyncToPriceBook_button_default"),
    TASK_MEMBER_PUBLISH("TaskMemberPublish", I18NKey.TASK_MEMBER_PUBLISH, "任务下发", "TaskMemberPublish_button_default"),
    // 9.2.5 合作伙伴新增按钮：申请延期
    RENEW_EXPIRATION("RenewExpiration", I18NKey.RENEW_EXPIRATION, "申请延期", "RenewExpiration_button_default"),
    ACC_RENEW_EXPIRATION("AccRenewExpiration", I18NKey.ACC_RENEW_EXPIRATION, "申请延期互联账号", "AccRenewExpiration_button_default"),
    CONFIG_PRINT_TEMPLATE("ConfigPrintTemplate", I18NKey.CONFIG_PRINT_TEMPLATE, "配置打印模版", "ConfigPrintTemplate_button_default"),
    CONFIG_BPM("ConfigBPM", I18NKey.CONFIG_BPM, "配置业务流程", "ConfigBPM_button_default"),
    SHARE_FRIENDS("ShareFriends", I18NKey.action_share_friends, "分享给好友", " ShareFriends_button_default"),
    TASK_POSTER_CHECK("TaskPosterCheck", I18NKey.TASK_POSTER_CHECK, "查看任务海报", " TaskPosterCheck_button_default"),

    // 930
    // 调拨确认出库
    CONFIRM_OUTBOUND("OutboundConfirmed", I18NKey.CONFIRM_OUTBOUND, "调拨确认出库", "OutboundConfirmed_button_default"),
    // 应收单完成
    COMPLETE("Complete", I18NKey.COMPLETE, "完成", "Complete_button_default"),
    // 应收单完成
    AUTO_MATCH("AutoMatch", I18NKey.AUTO_MATCH, "自动核销", "AutoMatch_button_default"),
    // 9.3.0 代理通渠道准入，申发起签约按钮。
    INITIATE_RENEWAL("InitiateRenewal", I18NKey.INITIATE_RENEWAL, "发起签约", "InitiateRenewal_button_default"),
    APPROVAL_DELAY_TASK_EXECUTE("ApprovalDelayTaskExecute", I18NKey.APPROVAL_DELAY_TASK_EXECUTE, "审批流-等待节点_立即执行"),
    BPM_DELAY_TASK_EXECUTE("BpmDelayTaskExecute", I18NKey.BPM_DELAY_TASK_EXECUTE, "业务流-等待节点_立即执行"),

    //940 结算单
    SETTLEMENTAR("SettlementAR", I18NKey.SETTLEMENTAR, "结算应收", "SettlementAR_button_default"),
    TASK_CLOSE("TaskClose", I18NKey.TASK_CLOSE, "任务终止", "TaskClose_button_default"),
    START_SETTLEMENT("StartSettlement", I18NKey.START_SETTLEMENT, "发起结算", "StartSettlement_button_default"),
    SUBMIT_ACCOUNT("SubmitAccount", I18NKey.SUBMIT_ACCOUNT, "交账", "SubmitAccount_button_default"),
    CLOSE_ACCOUNT("CloseAccount", I18NKey.CLOSE_ACCOUNT, "关帐", "CloseAccount_button_default"),
    SETTLE_ACCOUNT("SettleAccount", I18NKey.SETTLE_ACCOUNT, "结清", "SettleAccount_button_default"),
    REWARD_RECORD_PUBLISH("RewardRecordPublish", I18NKey.REWARD_RECORD_PUBLISH, "奖励发放", "RewardRecordPublish_button_default"),


    CANCEL_BUDGET_PROVISION("CancelBudgetProvision", I18NKey.CANCEL_BUDGET_PROVISION, "取消预提", "CancelBudgetProvision_button_default"),
    REUSE_PROVISION_OCCUPY("ReuseProvisionOccupy", I18NKey.REUSE_PROVISION_OCCUPY, "重新占用", "ReuseProvisionOccupy_button_default"),

    FREEZE_INVENTORY_BATCH_ADJUSTMENT("FreezeInventoryBatchAdjustment", I18NKey.FREEZE_INVENTORY_BATCH_ADJUSTMENT, "冻结库存批量调整", "FreezeInventoryBatchAdjustment_button_default"),
    ON_SHELF("OnShelf", I18NKey.ON_SHELF, "上架", "OnShelf_button_default"),
    OFF_SHELF("OffShelf", I18NKey.OFF_SHELF, "下架", "OffShelf_button_default"),
    //950手動核銷
    MANUAL_MATCH("ManualMatch", I18NKey.MANUAL_MATCH, "手动核销", "ManualMatch_button_default"),
    MODIFY_SETTLEMENT_RULES("ModifySettlementRules", I18NKey.MODIFY_SETTLEMENT_RULES, "修改结算规则", "ModifySettlementRules_button_default"),
    ADD_NON_STANDARD_PRODUCT("Add_Non_Standard_Product", I18NKey.ADD_NON_STANDARD_PRODUCT, "添加非标产品", "AddNonStandardProduct_button_default"),
    TRANSFER_ORDER("TransferOrder", I18NKey.TRANSFER_ORDER, "一键转单", "TransferOrder_button_default"),
    VIRTUAL_STOCK_IN("VirtualStockIn", I18NKey.VIRTUAL_STOCK_IN, "虚拟入库", "VirtualStockIn_button_default"),

    ADD_SUB_CONTRACT("AddSubContract", I18NKey.ADD_SUB_CONTRACT, "附加协议", "AddSubContract_button_default"),
    ENABLE_ACCOUNT_CHECK_RULE("EnableAccountCheckRule", I18NKey.ENABLE_ACCOUNT_CHECK_RULE, "启用", "EnableAccountCheckRule_button_default"),
    DISABLE_ACCOUNT_CHECK_RULE("DisableAccountCheckRule", I18NKey.DISABLE_ACCOUNT_CHECK_RULE, "停用", "DisableAccountCheckRule_button_default"),
    BOM_CORE_CONFIG_OCR("BomCoreConfigOcr", I18NKey.BOM_CORE_CONFIG_OCR, "配置爆炸图", "BomCoreConfigOcr_button_default"),
    APPLY_EXIST_ACCOUNTS("ApplyExistAccounts", I18NKey.APPLY_EXIST_ACCOUNTS, "对已有客户生效", "ApplyExistAccounts_button_default"),
    REWARD_RECORD_DETAIL_PUBLISH("RewardRecordDetailPublish", I18NKey.REWARD_RECORD_DETAIL_PUBLISH, "发放奖励", "RewardRecordDetailPublish_button_default"),

    SET_GOAL("SetGoal", I18NKey.SET_GOAL, "设置目标", "SetGoal_button_default"),

    VIEW_GOAL("ViewGoal", I18NKey.VIEW_GOAL, "查看进度", "ViewGoal_button_default"),
    RENEW("Renew", I18NKey.RENEW, "去续约", "Renew_button_default"),
    SYNC_QW("SyncToQw", I18NKey.SYNC_TO_QW, "同步到企微", "SyncToQw_button_default"),
    CREATE_SALE_OUTBOUND("CreateSaleOutbound", I18NKey.CREATE_SALE_OUTBOUND, "创建销售出库单", "CreateSaleOutbound_button_default"),
    // 方案生成器
    PROPOSAL_AI_GENERATE("ProposalAiGenerate", I18NKey.PROPOSAL_AI_GENERATE, "AI方案", "ProposalAiGenerate_button_default");

    private static Map<String, ObjectAction> actionMap = Maps.newHashMap();
    private static Map<String, ObjectAction> buttonActionMap = Maps.newHashMap();

    static {
        for (ObjectAction action : ObjectAction.values()) {
            actionMap.put(action.actionCode, action);
            if (!Strings.isNullOrEmpty(action.getButtonApiName())) {
                buttonActionMap.put(action.getButtonApiName(), action);
            }
        }
    }

    private String actionCode;
    private String actionLabelKey;
    private String defaultActionLabel;
    private String buttonApiName;

    ObjectAction(String actionCode, String actionLabelKey, String defaultActionLabel) {
        this.actionCode = actionCode;
        this.actionLabelKey = actionLabelKey;
        this.defaultActionLabel = defaultActionLabel;
    }

    ObjectAction(String actionCode, String actionLabelKey, String defaultActionLabel, String buttonApiName) {
        this.actionCode = actionCode;
        this.actionLabelKey = actionLabelKey;
        this.defaultActionLabel = defaultActionLabel;
        this.buttonApiName = buttonApiName;
    }

    public static ObjectAction of(String actionCode) {
        if ("Invalid".equals(actionCode)) {
            return INVALID;
        }
        ObjectAction action = actionMap.get(actionCode);
        if (action == null) {
            return UNKNOWN_ACTION;
        }
        return action;
    }

    public static List<String> getValues() {
        List<String> list = Lists.newArrayList();
        for (ObjectAction action : ObjectAction.values()) {
            list.add(action.getActionLabel());
        }
        return list;
    }

    public static List<ObjectAction> getAction() {
        return Lists.newArrayList(values());
    }

    public static boolean isRepeatLabel(IUdefButton button) {
        return Stream.of(values())
                .anyMatch(x -> Objects.equals(button.getLabel(), x.getActionLabel()) && !Objects.equals(x.getButtonApiName(), button.getApiName()));
    }

    public static boolean isCustomAction(String actionCode) {
        return StringUtils.endsWith(actionCode, "__c");
    }

    public static ObjectAction getByButtonApiName(String buttonApiName) {
        return buttonActionMap.getOrDefault(buttonApiName, UNKNOWN_ACTION);
    }

    public static String getActionCodeByButtonApiName(String buttonApiName) {
        ObjectAction action = ObjectAction.getByButtonApiName(buttonApiName);
        if (ObjectAction.UNKNOWN_ACTION == action) {
            return buttonApiName;
        } else {
            if (ObjectAction.CREATE_SAVE == action) {
                return ObjectAction.CREATE.getActionCode();
            }
            if (ObjectAction.UPDATE_SAVE == action) {
                return ObjectAction.UPDATE.getActionCode();
            }
            return action.getActionCode();
        }
    }

    public boolean labelIsDuplicate(IUdefButton button) {
        return getActionLabel().equals(button.getLabel()) && !Objects.equals(getButtonApiName(), button.getApiName());
    }

    public String getActionCode() {
        return actionCode;
    }

    public String getI18NKey() {
        return actionLabelKey;
    }

    public String getDefaultActionLabel() {
        return defaultActionLabel;
    }

    public String getActionLabel() {
        return I18NExt.getOrDefault(actionLabelKey, defaultActionLabel);
    }

    public String getActionLabel(String... placeHolder) {
        return I18NExt.getOrDefault(actionLabelKey, defaultActionLabel, placeHolder);
    }

    public String getInterfaceCode() {
        switch (this) {
            case INVALID:
            case BULK_INVALID:
                return "Invalid";
            default:
                return getActionCode();
        }
    }

    public String getPrivilegeCode(boolean isSFAObject) {
        switch (this) {
            case ADD_TEAM_MEMBER:
            case EDIT_TEAM_MEMBER:
            case DELETE_TEAM_MEMBER:
                return EDIT_TEAM_MEMBER.getActionCode();

            case RELATE:
            case BULK_RELATE:
            case BULK_DISRELATE:
                if (isSFAObject) {
                    return UPDATE.getActionCode();
                }
                return RELATE.getActionCode();

            case DELETE:
            case BULK_DELETE:
                return DELETE.getActionCode();

            case BULK_INVALID:
            case INVALID:
                return INVALID.getActionCode();

            case RECOVER:
            case BULK_RECOVER:
                return RECOVER.getActionCode();

            case CLONE:
                return CLONE.getActionCode();
            case AttributeEnable:
            case AttributeDisEnable:
                return AttributeEnable.getActionCode();
            case AssociateAttribute:
            case DisAssociateAttribute:
                return AssociateAttribute.getActionCode();
            case AssociateNonstandardAttribute:
            case DisAssociateNonstandardAttribute:
                return AssociateNonstandardAttribute.getActionCode();

            case CREATE:
            case CREATE_SAVE:
                return CREATE.getActionCode();

            case UPDATE:
            case UPDATE_SAVE:
                return UPDATE.getActionCode();
            case FAST_PRODUCT_ASSOCIATE_PRICE_BOOK:
                return FAST_PRODUCT_ASSOCIATE_PRICE_BOOK.getActionCode();
            case EDIT_PRICE_BOOK_PRODUCT:
                return EDIT_PRICE_BOOK_PRODUCT.getActionCode();
            case TRANSFER_UI:
            case TRANSFER:
                return TRANSFER.getActionCode();
            default:
                return this.getActionCode();
        }
    }

    public boolean isNeedDataPrivilege() {
        switch (this) {
            case SEND_MAIL:
            case DISCUSS:
            case REMIND:
            case SCHEDULE:
            case DIAL:
            case ALLOCATE_PROCUREMENT:
                return false;
            default:
                return true;
        }
    }

    public boolean needWriteDataPrivilege() {
        switch (this) {
            case BULK_HANG_TAG:
            case UPDATE:
            case CHANGE:
            case EFFECTIVE:
            case DELETE:
            case BULK_DELETE:
            case INVALID:
            case BULK_INVALID:
            case RECOVER:
            case BULK_RECOVER:
            case CHANGE_OWNER:
            case ADD_TEAM_MEMBER:
            case EDIT_TEAM_MEMBER:
            case DELETE_TEAM_MEMBER:
            case RELATE:
            case BULK_RELATE:
            case BULK_DISRELATE:
            case LOCK:
            case UNLOCK:
            case MODIFYLOG_RECOVER:
            case FOLLOW_UP:
            case RETURN:
            case TAKE_BACK:
            case ALLOCATE:
            case MOVE:
            case REMOVE:
            case DEAL:
            case CLOSE:
            case UPLOAD_DELETE_ATTACH:
            case PRIORITY:
            case AttributeEnable:
            case AttributeDisEnable:
            case AssociateAttribute:
            case DisAssociateAttribute:
            case AssociateNonstandardAttribute:
            case DisAssociateNonstandardAttribute:
            case FAST_PRODUCT_ASSOCIATE_PRICE_BOOK:
            case EDIT_PRICE_BOOK_PRODUCT:
            case UPDATE_GDPR:
            case CHANGE_PARTNER_OWNER:
                return true;
            default:
                return false;
        }
    }

    public IButton createButton() {
        IButton button = new Button();
        button.setName(getDefaultButtonApiName());
        button.setAction(getActionCode());
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        button.setLabel(getActionLabel());
        return button;
    }

    public String getDefaultButtonApiName() {
        return Strings.isNullOrEmpty(buttonApiName) ? buildDefaultButtonApiName() : buttonApiName;
    }

    private String buildDefaultButtonApiName() {
        return getActionCode() + "_button_" + IButton.ACTION_TYPE_DEFAULT;
    }

    public String getButtonApiName() {
        return buttonApiName;
    }

    public String getBulkButtonApiName() {
        if (this == INVALID) {
            return "AsyncBulk" + "Invalid";
        } else if (this == BULK_HANG_TAG || this == SEND_MAIL) {
            return this.getActionCode();
        }
        return "AsyncBulk" + getActionCode();
    }
}
